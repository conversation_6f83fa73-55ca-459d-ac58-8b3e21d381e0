import { ComponentFixture, TestBed } from '@angular/core/testing';

import { SoftwareBuildFilterComponent } from './software-build-filter.component';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { CountryCacheService } from 'src/app/shared/Service/CacheService/countrycache.service';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { SoftwareBuildOperationsService } from '../software-build-services/software-build-operations.service';
import { DatePipe } from '@angular/common';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { RoleApiCallService } from 'src/app/shared/Service/RoleService/role-api-call.service';
import { ConfirmDialogService } from 'src/app/confirmationdialog/confirmation.service';
import { HidePermissionNamePipe } from 'src/app/shared/pipes/Role/hidePermissionName.pipe';
import { DeviceOperationService } from '../../Device/DeviceService/Device-Operation/device-operation.service';
import { EnumMappingDisplayNamePipe } from 'src/app/shared/pipes/Common/EnumMappingDisplayNamePipe.pipe';
import { PrintListPipe } from 'src/app/shared/pipes/printList.pipe';

describe('SoftwareBuildFilterComponent', () => {
  let component: SoftwareBuildFilterComponent;
  let fixture: ComponentFixture<SoftwareBuildFilterComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [SoftwareBuildFilterComponent],
      imports: [],
      providers: [
        CountryCacheService,
        LocalStorageService,
        SessionStorageService,
        SoftwareBuildOperationsService,
        DatePipe,
        CommonOperationsService,
        RoleApiCallService,
        ConfirmDialogService,
        HidePermissionNamePipe,
        DeviceOperationService,
        EnumMappingDisplayNamePipe,
        PrintListPipe,
        commonsProviders(null)]
    })
      .compileComponents();

    fixture = TestBed.createComponent(SoftwareBuildFilterComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
